# TicTaps Bingo - Phaser JS

A port of the Unity Bingo game to Phaser JS using TypeScript and Phaser 3.

## Overview

This game is a simple Bingo-style matching game where numbers appear on the right side of the screen, and the player needs to find and click matching numbers on the Bingo board. Scoring is based on how quickly the player matches the numbers.

## Game Mechanics

### Core Gameplay
- Players match numbers that appear in the right panel with numbers on the 5x5 Bingo board
- Each game lasts 30 seconds
- The faster you match a number, the higher your score
- Numbers appear one at a time in the right panel and move down as new numbers appear
- When a number's timer expires, the matching cell on the board starts blinking
- When a number reaches the center position, it highlights the matching cell on the board

### Scoring System
- Score is based on how quickly you match a number
- Matching within the first 10% of the timer: 100 points
- Matching within the first 20% of the timer: 90 points
- Matching within the first 30% of the timer: 80 points
- And so on, down to 10 points for matching after the timer expires

### Visual Effects
- Countdown sequence (3, 2, 1, GO) at the start of the game
- Particle effects when matching numbers
- Celebration effects when the game ends
- Timer color changes (blue → yellow → red) as time runs out
- Score popup animations when points are earned

### Sound Effects
- Countdown sounds
- Number appearance sound
- Match sound
- Win sound
- Click sound

## Project Structure

```
├── src/
│   ├── objects/          # Game object classes
│   │   ├── BingoCell.ts  # Individual cell on bingo board
│   │   └── RightNumber.ts # Number in right panel
│   ├── scenes/           # Game scenes
│   │   ├── PreloadScene.ts # Asset loading
│   │   ├── GameStartScene.ts  # Title screen
│   │   └── GameScene.ts   # Main game
│   ├── utils/            # Utility classes
│   │   └── TicTapsConnector.ts # Communication with TicTaps
│   ├── index.html        # HTML entry point
│   └── index.ts          # TypeScript entry point
├── assets/               # Game assets
│   ├── audio/            # Sound effects
│   ├── fonts/            # Web fonts
│   └── images/           # Game images
├── package.json          # NPM package file
├── tsconfig.json         # TypeScript configuration
└── webpack.config.js     # Webpack configuration
```

## Development

### Prerequisites

- Node.js (>= 14.x)
- NPM (>= 7.x)

### Setup

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Start development server:

```bash
npm start
```

4. Build for production:

```bash
npm run build
```

## Project Settings

### TypeScript Configuration
- Target: ES2017
- Module: ES2015
- Strict type checking enabled
- Code quality checks:
  - No unused locals
  - No unused parameters
  - No implicit returns
  - No fallthrough cases in switch statements

### Webpack Configuration
- Entry point: ./src/index.ts
- Output: dist/bundle.js
- Development server on port 8080 with hot reloading
- Assets are copied from src/assets to dist/src/assets

## Code Style

The project follows these code style patterns:

- **TypeScript Classes**: All game objects are implemented as TypeScript classes that extend Phaser's built-in classes
- **Strong Typing**: Type annotations are used throughout the codebase
- **Documentation**: JSDoc comments describe the purpose and functionality of classes and methods
- **Unity Equivalence**: Comments reference equivalent functionality in the Unity version
- **Consistent Naming**: 
  - Classes use PascalCase (e.g., BingoCell)
  - Methods and properties use camelCase (e.g., createVisuals)
  - Private properties are prefixed with underscore or marked with the private keyword
- **Object-Oriented Design**: Encapsulation of functionality within appropriate classes
- **Separation of Concerns**: Game logic, visual elements, and platform integration are separated

## Integration with TicTaps

This game is designed to be embedded within the TicTaps platform. Communication with the parent window is handled through the `TicTapsConnector` class.

Key integration points:
- `notifyGameReady()`: Notifies the parent that the game is ready to play
- `sendScore(score)`: Sends the final score to the parent
- `notifyGameQuit()`: Notifies the parent that the game has ended
