import Phaser from 'phaser';

/**
 * PreloadScene - Loads all assets before game starts
 */
export default class PreloadScene extends Phaser.Scene {
  constructor() {
    super('PreloadScene');
  }

  preload() {
    const { width, height } = this.cameras.main;

    // Create loading bar
    const bgBar = this.add.rectangle(
        width / 2,
        height / 2,
        width / 2,
        20,
        0x232323
    );

    const progressBar = this.add.rectangle(
        bgBar.x - bgBar.width / 2,
        bgBar.y,
        0,
        bgBar.height,
        0x00ff00
    );
    progressBar.setOrigin(0, 0.5);

    const loadingText = this.add.text(
        width / 2,
        height / 2 - 30,
        'Loading...',
        {
          font: '24px Arial',
          color: '#ffffff'
        }
    ).setOrigin(0.5);

    // Update progress bar as assets are loaded
    this.load.on('progress', (value: number) => {
      progressBar.width = bgBar.width * value;
    });

    // Remove progress bar when complete
    this.load.on('complete', () => {
      progressBar.destroy();
      bgBar.destroy();
      loadingText.destroy();
    });

    // Load images
    this.load.image('game_background', 'src/assets/images/game_bg.png');
    this.load.image('countdown-3', 'src/assets/images/countdown-3.png');
    this.load.image('countdown-2', 'src/assets/images/countdown-2.png');
    this.load.image('countdown-1', 'src/assets/images/countdown-1.png');
    this.load.image('countdown-go', 'src/assets/images/countdown-go.png');
    
    // GameStartScene assets
    this.load.image('game_name', 'src/assets/images/game_name.svg');
    this.load.image('button_bg', 'src/assets/images/button_bg.svg');
    this.load.image('game_start', 'src/assets/images/game_start.png');
    
    // GameEndScene assets
    this.load.image('game_over', 'src/assets/images/game_over.svg');
    this.load.image('back_to_lobby', 'src/assets/images/back_to_lobby.png');

    // Load audio
    this.load.audio('click', ['src/assets/audio/click.mp3', 'src/assets/audio/click.ogg']);
    this.load.audio('match', ['src/assets/audio/match.mp3', 'src/assets/audio/match.ogg']);
    this.load.audio('countdown', ['src/assets/audio/countdown.mp3', 'src/assets/audio/countdown.ogg']);
    this.load.audio('go', ['src/assets/audio/go.mp3', 'src/assets/audio/go.ogg']);
    this.load.audio('win', ['src/assets/audio/win.mp3', 'src/assets/audio/win.ogg']);
    this.load.audio('number-appear', ['src/assets/audio/number_appear.mp3', 'src/assets/audio/number_appear.ogg']);
  }

  create() {
    this.scene.start('GameStartScene');
  }
}