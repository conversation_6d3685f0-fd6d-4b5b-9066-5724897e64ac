{"compilerOptions": {"target": "es2017", "module": "es2015", "lib": ["dom", "es2017"], "declaration": true, "outDir": "./dist", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}