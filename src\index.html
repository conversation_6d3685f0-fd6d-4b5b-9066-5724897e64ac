<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  
  <!-- iOS-specific settings -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-touch-fullscreen" content="yes">
  
  <!-- Disable auto-detection -->
  <meta name="format-detection" content="telephone=no">
  
  <title>TicTaps Bingo</title>
  <link rel="stylesheet" href="src/assets/fonts/fonts.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      /* Mobile optimizations */
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      touch-action: manipulation;
      -webkit-tap-highlight-color: transparent;
    }

    html, body {
      width: 100%;
      height: 100%;
      overflow: hidden;
      background-color: #000;
      position: fixed;
      overscroll-behavior: none;
    }

    body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      position: fixed;
      width: 100vw;
      height: 100vh;
    }

    #game-container {
      width: 100vw;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    /* Force canvas centering - critical fix */
    #game-container > canvas {
      position: absolute !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
      margin: 0 !important;
    }

    /* Font preloading div (hidden) */
    .font-preload {
      position: absolute;
      top: -9999px;
      left: -9999px;
      font-family: 'TT Neoris', Arial, sans-serif;
      font-weight: bold;
    }
    
    /* Remove focus outlines */
    button:focus, a:focus, *:focus {
      outline: none;
    }
  </style>
</head>
<body>
<div id="game-container"></div>
<div class="font-preload">Font Preload</div>

<script>
  // Prevent iOS double-tap to zoom
  let lastTouchEnd = 0;
  document.addEventListener('touchend', function (event) {
    const now = (new Date()).getTime();
    if (now - lastTouchEnd <= 300) {
      event.preventDefault();
    }
    lastTouchEnd = now;
  }, false);

  // Prevent pinch zoom
  document.addEventListener('touchmove', function(event) {
    if (event.scale !== 1) {
      event.preventDefault();
    }
  }, { passive: false });

  // Prevent iOS gesture defaults
  document.addEventListener('gesturestart', function(e) {
    e.preventDefault();
  });
</script>
</body>
</html>