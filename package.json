{"name": "tictaps-bingo-phaser", "version": "1.0.0", "description": "TicTaps Bingo game ported to Phaser JS with TypeScript", "main": "src/index.ts", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production"}, "keywords": ["phaser", "bingo", "game", "tictaps", "typescript"], "author": "", "license": "ISC", "dependencies": {"phaser": "^3.60.0"}, "devDependencies": {"@types/node": "^18.0.0", "copy-webpack-plugin": "^11.0.0", "html-webpack-plugin": "^5.5.0", "image-minimizer-webpack-plugin": "^4.1.3", "imagemin": "^9.0.1", "imagemin-gifsicle": "^7.0.0", "imagemin-jpegtran": "^8.0.0", "imagemin-optipng": "^8.0.0", "imagemin-svgo": "^11.0.1", "ts-loader": "^9.4.0", "typescript": "^5.0.0", "webpack": "^5.76.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1"}}