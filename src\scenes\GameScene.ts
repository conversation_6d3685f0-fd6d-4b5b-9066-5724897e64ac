import Phaser from 'phaser';
import BingoCell from '../objects/BingoCell';
import RightNumber from '../objects/RightNumber';

/**
 * GameScene - Main game scene
 * Equivalent to GameController in Unity
 */
export default class GameScene extends Phaser.Scene {
  private score!: number;
  public gameEnd!: boolean;
  private rightTurnCount!: number;
  private bingoLetters!: string[];
  private bgoCells!: BingoCell[];
  private rightNumbers!: RightNumber[];
  private rightPositions!: { x: number; y: number }[];
  private scoreText!: Phaser.GameObjects.Text;
  private timerText!: Phaser.GameObjects.Text;
  private scoreLabel!: Phaser.GameObjects.Text;
  private timerLabel!: Phaser.GameObjects.Text;

  private countdownPanel!: Phaser.GameObjects.Container;
  private countdownText!: Phaser.GameObjects.Image;
  private gameTimerEvent!: Phaser.Time.TimerEvent | null;
  private numbers!: number[];
  private isRightItemScheduled: boolean;

  // Add camera dimensions for responsive layout
  private cameraWidth!: number;
  private cameraHeight!: number;
  private centerX!: number;
  private centerY!: number;

  constructor() {
    super('GameScene');
    this.isRightItemScheduled = false;
  }

  create() {
    // Set camera dimensions and center points
    this.cameraWidth = this.cameras.main.width;
    this.cameraHeight = this.cameras.main.height;
    this.centerX = this.cameraWidth / 2;
    this.centerY = this.cameraHeight / 2;

    // Initialize game variables
    this.score = 0;
    this.gameEnd = false;
    this.rightTurnCount = 0;
    this.bingoLetters = ['B', 'I', 'N', 'G', 'O'];
    this.bgoCells = [];
    this.rightNumbers = [];

    // Calculate right positions
    this.rightPositions = this.calculateRightPositions();

    // Setup UI elements
    this.createBackground();
    this.createUI();

    // TESTING: Skip countdown and start game immediately
    // this.initializeGame();
    this.startCountdown();
  }

  /**
   * Create game background - using game_bg.png
   */
  createBackground() {
    // Add the game background image
    this.add.image(0, 0, 'game_background')
        .setOrigin(0, 0)
        .setDisplaySize(this.cameraWidth, this.cameraHeight);
  }

  /**
   * Create UI elements
   */
  createUI() {
    // Create score display
    this.createScoreDisplay();

    // Create game timer
    this.createGameTimer();

    // Create countdown overlay (initially hidden)
    this.createCountdownOverlay();

    // Hide UI elements initially - they will be shown after countdown
    this.hideGameUI();
  }

  /**
   * Create score display - updated to match Unity
   */
  createScoreDisplay() {
    // Score label
    this.scoreLabel = this.add.text(30, 30, 'Score', {
      fontFamily: '"TT Neoris", Arial, sans-serif',
      fontSize: '32px',
      color: '#389CFF',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 1
    });

    // Score text
    this.scoreText = this.add.text(30, 70, '0', {
      fontFamily: '"TT Neoris", Arial, sans-serif',
      fontSize: '48px',
      color: '#389CFF',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 1
    });
  }

  /**
   * Create game timer display - updated to match Unity
   */
  createGameTimer() {
    // Timer label - position from the right edge
    this.timerLabel = this.add.text(this.cameraWidth - 130, 30, 'Time', {
      fontFamily: '"TT Neoris", Arial, sans-serif',
      fontSize: '32px',
      color: '#389CFF',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 1
    }).setOrigin(0, 0);

    // Timer text - position from the right edge
    this.timerText = this.add.text(this.cameraWidth - 130, 70, '30', {
      fontFamily: '"TT Neoris", Arial, sans-serif',
      fontSize: '48px',
      color: '#389CFF',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 1
    }).setOrigin(0, 0);
  }

  /**
   * Create countdown overlay (initially hidden)
   * Using countdown images like in matching mayhem
   */
  createCountdownOverlay() {
    // Create countdown panel container
    this.countdownPanel = this.add.container(0, 0);
    this.countdownPanel.setDepth(2); // Ensure countdown panel is above everything

    // Add semi-transparent overlay
    const overlay = this.add.rectangle(
      0, 0, 
      this.cameraWidth, 
      this.cameraHeight, 
      0x000000, 0.7
    ).setOrigin(0, 0);
    this.countdownPanel.add(overlay);

    // Create countdown image (initially hidden, will be updated during countdown)
    this.countdownText = this.add.image(
      this.centerX,
      this.centerY,
      'countdown-3'
    ).setScale(0).setOrigin(0.5);
    this.countdownPanel.add(this.countdownText);
  }

  /**
   * Start countdown sequence
   * Using image-based countdown like in matching mayhem
   */
  async startCountdown() {
    // Show countdown panel
    this.countdownPanel.visible = true;

    // Countdown sequence with images
    const countdownImages = ['countdown-3', 'countdown-2', 'countdown-1', 'countdown-go'];

    for (let i = 0; i < countdownImages.length; i++) {
      await this.playCountdownStep(countdownImages[i], i === countdownImages.length - 1);
    }

    // Hide countdown panel and start game
    this.countdownPanel.visible = false;
    this.showGameUI();
    this.initializeGame();
  }

  /**
   * Play a single step of the countdown animation
   */
  private playCountdownStep(texture: string, isGo: boolean): Promise<void> {
    return new Promise((resolve) => {
      // Update image texture
      this.countdownText.setTexture(texture);
      this.countdownText.setScale(0);

      // Play appropriate sound
      try {
        this.sound.play(isGo ? 'go' : 'countdown');
      } catch (error) {
        console.warn('Sound playback failed:', error);
      }

      // Scale up animation
      this.tweens.add({
        targets: this.countdownText,
        scale: 0.2, // Same scale as matching mayhem
        duration: 300,
        ease: 'Back.easeOut',
        onComplete: () => {
          // Hold for a moment, then scale down
          this.time.delayedCall(700, () => {
            this.tweens.add({
              targets: this.countdownText,
              scale: 0,
              duration: 300,
              ease: 'Back.easeIn',
              onComplete: () => resolve()
            });
          });
        }
      });
    });
  }

  /**
   * Initialize the game after countdown
   */
  initializeGame() {
    // Generate random bingo numbers
    this.generateRandomNumbers();

    // Create bingo board
    this.createBingoBoard();

    // Start game timer
    this.startGameTimer();

    // Set initial right turn count
    this.rightTurnCount = Phaser.Math.Between(2, 4);

    // Add first right number after delay
    this.time.delayedCall(1500, () => {
      this.addRightItem();
    });
  }

  /**
   * Generate random numbers for the bingo board
   */
  generateRandomNumbers() {
    this.numbers = [];

    // Generate 25 unique random numbers
    while (this.numbers.length < 25) {
      const num = Phaser.Math.Between(1, 99);
      if (!this.numbers.includes(num)) {
        this.numbers.push(num);
      }
    }
  }

  /**
   * Create the bingo board with random numbers
   */
  createBingoBoard() {
    const cellSize = 80;
    const cellSpacing = 10;
    const totalWidth = (cellSize + cellSpacing) * 5 - cellSpacing;
    const startX = this.centerX - totalWidth / 2; // Center the board horizontally

    // Calculate vertical positions based on camera height
    const boardCenterY = this.centerY + 50; // Slightly below center
    const headerY = boardCenterY - (cellSize + cellSpacing) * 2.5;
    const gridStartY = headerY + (cellSize + cellSpacing);

    // Create the BINGO header row
    for (let col = 0; col < 5; col++) {
      const x = startX + col * (cellSize + cellSpacing) + cellSize / 2;
      const y = headerY;

      // Create a graphics object for the rounded rectangle
      const graphics = this.add.graphics();

      // Use gradient fill for the header
      graphics.fillGradientStyle(
          0x3066FF, // Top-left: blue
          0x4752FF, // Top-right: blue-purple
          0x5E4DFF, // Bottom-right: more purple
          0x215EFF, // Bottom-left: blue
          1
      );
      graphics.lineStyle(3, 0x00E5AE, 1);

      // Draw rounded rectangle centered at (x, y)
      graphics.fillRoundedRect(x - cellSize/2, y - cellSize/2, cellSize, cellSize, 16);
      graphics.strokeRoundedRect(x - cellSize/2, y - cellSize/2, cellSize, cellSize, 16);

      // Add letter
      this.add.text(
          x,
          y,
          this.bingoLetters[col],
          {
            fontFamily: '"TT Neoris", Arial, sans-serif',
            fontSize: '48px',
            color: '#FFFFFF',
            align: 'center',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 3,
            shadow: { offsetX: 2, offsetY: 2, color: '#000000', blur: 2, fill: true }
          }
      ).setOrigin(0.5);
    }

    // Create the main number grid - with animations
    let numberIndex = 0;

    // Create 5x5 grid of bingo cells (excluding the header row)
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const x = startX + col * (cellSize + cellSpacing) + cellSize / 2;
        const y = gridStartY + row * (cellSize + cellSpacing);

        // Create cell with letter and number
        const cell = new BingoCell(
            this,
            x,
            y,
            this.bingoLetters[col],
            this.numbers[numberIndex]
        );

        // Hide letter for main board
        cell.letterText.alpha = 0;

        // Initially set scale to 0 for animation
        cell.setScale(0);

        // Add to scene and store reference
        this.bgoCells.push(cell);

        // Create staggered animation for each cell based on row and column
        // This creates a wave-like effect as cells appear
        this.time.delayedCall(150 * row + 50 * col, () => {
          this.tweens.add({
            targets: cell,
            scale: 1,
            duration: 300,
            ease: 'Back.Out'
          });
        });

        numberIndex++;
      }
    }
  }

  /**
   * Start the game timer
   * Equivalent to TimerCoroutine in Unity
   */
  startGameTimer() {
    const totalSeconds = 300;
    let timeRemaining = totalSeconds;

    // Update timer display once without creating the timer event
    this.updateGameTimer(timeRemaining, totalSeconds);

    // TESTING: Disable timer countdown
    this.gameTimerEvent = this.time.addEvent({
      delay: 1000,
      callback: () => {
        // Decrease time
        timeRemaining--;

        // Update timer display
        this.updateGameTimer(timeRemaining, totalSeconds);

        // Check if timer has reached zero
        if (timeRemaining <= 0) {
          this.endGame();
        }
      },
      repeat: totalSeconds - 1
    });
  }

  /**
   * Update game timer display
   * @param {number} timeRemaining - Seconds remaining
   * @param {number} _totalSeconds - Total seconds for the timer (unused)
   */
  private updateGameTimer(timeRemaining: number, _totalSeconds: number): void {
    // Update timer text
    this.timerText.setText(timeRemaining.toString());

    // Change color based on time remaining
    if (timeRemaining <= 10) {
      this.timerText.setColor('#FF4D4D'); // Red
    } else if (timeRemaining <= 20) {
      this.timerText.setColor('#FFCC00'); // Yellow
    } else {
      this.timerText.setColor('#389CFF'); // Blue
    }
  }

  /**
   * Calculate positions for right panel items
   */
  private calculateRightPositions(): { x: number; y: number }[] {
    const positions = [];
    const cellSize = 80;
    const cellSpacing = 10;
    const totalWidth = (cellSize + cellSpacing) * 5 - cellSpacing;
    const startX = this.centerX - totalWidth / 2; // Center horizontally

    // Position between header and timer
    const panelY = 170;

    // Create positions for 5 items (0-4 indices)
    for (let i = 0; i < 5; i++) {
      positions.push({
        x: startX + i * (cellSize + cellSpacing) + cellSize / 2,
        y: panelY
      });
    }

    return positions;
  }

  /**
   * Get position for right panel item by index
   * @param {number} index - Position index
   * @returns {object} Position with x and y coordinates
   */
  public getRightPosition(index: number): { x: number; y: number } {
    if (index >= 0 && index < this.rightPositions.length) {
      return this.rightPositions[index];
    }
    return { x: this.centerX + 100, y: this.centerY };
  }

  /**
   * Add a new number to the right panel
   * Equivalent to addRightItem in Unity
   */
  private addRightItem(): void {
    if (this.gameEnd) return;

    // Play sound
    this.sound.play('number-appear');

    // Move existing right numbers down
    this.moveExistingRightItems();

    // Get position for new item
    const position = this.getRightPosition(4);

    // Get a random bingo cell that hasn't been marked yet
    const availableCells = this.bgoCells.filter(cell => !cell.marked);

    if (availableCells.length === 0) {
      // If no cells are available, end the game
      this.endGame();
      return;
    }

    // Get a random unmarked cell
    const randomCell = Phaser.Utils.Array.GetRandom(availableCells);

    // Create new right number
    const rightNumber = new RightNumber(
        this,
        position.x,
        position.y,
        4,
        randomCell.letter,
        randomCell.number
    );

    // Add to right panel
    this.rightNumbers.push(rightNumber);

    // Set the flag to indicate a right item is scheduled
    this.isRightItemScheduled = true;

    // Schedule next right item
    this.time.delayedCall(2400, () => {
      if (!this.gameEnd) {
        this.isRightItemScheduled = false;
        this.addRightItem();
      }
    });
  }

  /**
   * Move existing right items down
   */
  moveExistingRightItems() {
    for (const item of this.rightNumbers) {
      // Decrease index
      item.moveToPosition(item.rightIndex - 1);
    }

    // Filter out destroyed items
    this.rightNumbers = this.rightNumbers.filter(item => item.rightIndex >= 0);
  }

  /**
   * Check if a cell matches any right panel item
   * @param {BingoCell} cell - The cell to check
   */
  public checkForMatch(cell: BingoCell): void {
    if (this.gameEnd) return;

    // Find matching right number
    const matchingNumber = this.rightNumbers.find(item => item.name === cell.name);

    if (matchingNumber) {
      // Mark the cell
      cell.mark();

      // Remove any highlight effects (if they exist)
      this.children.list.forEach(child => {
        if (child.type === 'Graphics' &&
            (child as any).x === cell.x - 40 &&
            (child as any).y === cell.y - 40) {
          child.destroy();
        }
      });

      // Add score based on timer value
      this.addScore(matchingNumber.getScore());

      // Play sound
      this.sound.play('match');

      // Remove the matching number from the array
      this.rightNumbers = this.rightNumbers.filter(item => item !== matchingNumber);

      // Destroy the matching number
      matchingNumber.destroy();

      // Check if we need to add a new item
      this.rightTurnCount--;
      if (this.rightTurnCount <= 0 && !this.gameEnd && !this.isRightItemScheduled) {
        this.time.delayedCall(450, () => {
          if (!this.isRightItemScheduled) {
            this.addRightItem();
          }
        });
      }
    }
  }

  /**
   * Find a bingo cell by name
   * @param {string} name - The name of the cell (e.g., "B12")
   * @returns {BingoCell|null} The matching cell or null if not found
   */
  public findBingoCellByName(name: string): BingoCell | null {
    return this.bgoCells.find(cell => cell.name === name) || null;
  }

  /**
   * Add score and update display
   * @param {number} value - Score to add
   */
  private addScore(value: number): void {
    this.score += value;

    // Update score display
    this.scoreText.setText(this.score.toString());

    // Create score popup
    this.createScorePopup(value);
  }

  /**
   * Create score popup animation
   * @param {number} value - Score value to show
   */
  private createScorePopup(value: number): void {
    // Create text - position dynamically
    const popup = this.add.text(this.centerX, 80, `+${value}`, {
      fontFamily: '"TT Neoris", Arial, sans-serif',
      fontSize: '32px',
      color: '#4CAF50',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // Animate popup
    this.tweens.add({
      targets: popup,
      y: '-=50',
      alpha: { from: 1, to: 0 },
      scale: { from: 1, to: 1.5 },
      duration: 1000,
      ease: 'Power2',
      onComplete: () => {
        popup.destroy();
      }
    });
  }

  /**
   * End the game
   * Equivalent to when GameEnd is set to true in Unity
   */
  endGame() {
    if (this.gameEnd) return;

    // Set game end flag
    this.gameEnd = true;

    // Stop game timer
    if (this.gameTimerEvent) {
      this.gameTimerEvent.remove();
    }

    // Create celebration effects
    this.createCelebrationEffects();

    // Transition to GameEndScene after delay
    this.time.delayedCall(1000, () => {
      this.scene.start('GameEndScene', { score: this.score });
    });
  }

  /**
   * Create celebration effects for game end
   * Equivalent to HandleGameEnd in Unity
   */
  createCelebrationEffects() {
    // Play win sound
    this.sound.play('win');

    // Create particles on each cell
    for (const cell of this.bgoCells) {
      // Mark unmarked cells
      if (!cell.marked) {
        cell.mark();
      }

      // Add delayed win particles
      this.time.delayedCall(Phaser.Math.Between(0, 1000), () => {
        cell.createWinParticles();
      });
    }
  }

  /**
   * Hide game UI elements during countdown
   */
  private hideGameUI(): void {
    this.scoreLabel.setVisible(false);
    this.scoreText.setVisible(false);
    this.timerLabel.setVisible(false);
    this.timerText.setVisible(false);
  }

  /**
   * Show game UI elements after countdown
   */
  private showGameUI(): void {
    this.scoreLabel.setVisible(true);
    this.scoreText.setVisible(true);
    this.timerLabel.setVisible(true);
    this.timerText.setVisible(true);
  }
}
