import Phaser from 'phaser';
import BingoCell from '../objects/BingoCell';
import RightNumber from '../objects/RightNumber';

/**
 * Standard bingo column letter type
 */
type BingoColumn = 'B' | 'I' | 'N' | 'G' | 'O';

/**
 * Bingo number ranges for each column
 */
interface BingoColumnRanges {
  B: { min: 1; max: 15 };
  I: { min: 16; max: 30 };
  N: { min: 31; max: 45 };
  G: { min: 46; max: 60 };
  O: { min: 61; max: 75 };
}

/**
 * Bingo card cell data structure
 */
interface BingoCellData {
  column: BingoColumn;
  number: number;
  row: number;
  col: number;
  isFree: boolean;
}

/**
 * Called bingo number with metadata
 */
interface CalledNumber {
  column: BingoColumn;
  number: number;
  callOrder: number;
  timestamp: number;
}

/**
 * Winning pattern types
 */
type WinningPattern = 'horizontal' | 'vertical' | 'diagonal' | 'fourCorners' | 'fullCard';

/**
 * Winning pattern result
 */
interface WinResult {
  hasWon: boolean;
  pattern?: WinningPattern;
  winningCells?: BingoCellData[];
}

/**
 * GameScene - Main game scene implementing standard bingo rules
 * Equivalent to GameController in Unity
 */
export default class GameScene extends Phaser.Scene {
  // Standard bingo constants
  private static readonly BINGO_COLUMNS: BingoColumn[] = ['B', 'I', 'N', 'G', 'O'];
  private static readonly COLUMN_RANGES: BingoColumnRanges = {
    B: { min: 1, max: 15 },
    I: { min: 16, max: 30 },
    N: { min: 31, max: 45 },
    G: { min: 46, max: 60 },
    O: { min: 61, max: 75 }
  };
  private static readonly FREE_SPACE_POSITION = { row: 2, col: 2 }; // Center of 5x5 grid

  // Game state properties
  private score!: number;
  public gameEnd!: boolean;
  private rightTurnCount!: number;

  // Bingo-specific properties
  private bingoCard!: BingoCellData[][];
  private calledNumbers!: CalledNumber[];
  private availableNumbers!: number[];
  private currentCallOrder!: number;

  // Game objects
  private bgoCells!: BingoCell[];
  private rightNumbers!: RightNumber[];
  private rightPositions!: { x: number; y: number }[];

  // UI elements
  private scoreText!: Phaser.GameObjects.Text;
  private timerText!: Phaser.GameObjects.Text;
  private scoreLabel!: Phaser.GameObjects.Text;
  private timerLabel!: Phaser.GameObjects.Text;
  private countdownPanel!: Phaser.GameObjects.Container;
  private countdownText!: Phaser.GameObjects.Image;

  // Timers and events
  private gameTimerEvent!: Phaser.Time.TimerEvent | null;
  private isRightItemScheduled: boolean;

  // Add camera dimensions for responsive layout
  private cameraWidth!: number;
  private cameraHeight!: number;
  private centerX!: number;
  private centerY!: number;

  constructor() {
    super('GameScene');
    this.isRightItemScheduled = false;
  }

  create() {
    // Set camera dimensions and center points
    this.cameraWidth = this.cameras.main.width;
    this.cameraHeight = this.cameras.main.height;
    this.centerX = this.cameraWidth / 2;
    this.centerY = this.cameraHeight / 2;

    // Initialize game variables
    this.score = 0;
    this.gameEnd = false;
    this.rightTurnCount = 0;
    this.bgoCells = [];
    this.rightNumbers = [];

    // Initialize bingo-specific data
    this.calledNumbers = [];
    this.currentCallOrder = 0;
    this.initializeAvailableNumbers();

    // Calculate right positions
    this.rightPositions = this.calculateRightPositions();

    // Setup UI elements
    this.createBackground();
    this.createUI();

    // TESTING: Skip countdown and start game immediately
    // this.initializeGame();
    this.startCountdown();
  }

  /**
   * Create game background - using game_bg.png
   */
  createBackground() {
    // Add the game background image
    this.add.image(0, 0, 'game_background')
        .setOrigin(0, 0)
        .setDisplaySize(this.cameraWidth, this.cameraHeight);
  }

  /**
   * Create UI elements
   */
  createUI() {
    // Create score display
    this.createScoreDisplay();

    // Create game timer
    this.createGameTimer();

    // Create countdown overlay (initially hidden)
    this.createCountdownOverlay();

    // Hide UI elements initially - they will be shown after countdown
    this.hideGameUI();
  }

  /**
   * Create score display - updated to match Unity
   */
  createScoreDisplay() {
    // Score label
    this.scoreLabel = this.add.text(30, 30, 'Score', {
      fontFamily: '"TT Neoris", Arial, sans-serif',
      fontSize: '32px',
      color: '#389CFF',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 1
    });

    // Score text
    this.scoreText = this.add.text(30, 70, '0', {
      fontFamily: '"TT Neoris", Arial, sans-serif',
      fontSize: '48px',
      color: '#389CFF',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 1
    });
  }

  /**
   * Create game timer display - updated to match Unity
   */
  createGameTimer() {
    // Timer label - position from the right edge
    this.timerLabel = this.add.text(this.cameraWidth - 130, 30, 'Time', {
      fontFamily: '"TT Neoris", Arial, sans-serif',
      fontSize: '32px',
      color: '#389CFF',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 1
    }).setOrigin(0, 0);

    // Timer text - position from the right edge
    this.timerText = this.add.text(this.cameraWidth - 130, 70, '30', {
      fontFamily: '"TT Neoris", Arial, sans-serif',
      fontSize: '48px',
      color: '#389CFF',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 1
    }).setOrigin(0, 0);
  }

  /**
   * Create countdown overlay (initially hidden)
   * Using countdown images like in matching mayhem
   */
  createCountdownOverlay() {
    // Create countdown panel container
    this.countdownPanel = this.add.container(0, 0);
    this.countdownPanel.setDepth(2); // Ensure countdown panel is above everything

    // Add semi-transparent overlay
    const overlay = this.add.rectangle(
      0, 0, 
      this.cameraWidth, 
      this.cameraHeight, 
      0x000000, 0.7
    ).setOrigin(0, 0);
    this.countdownPanel.add(overlay);

    // Create countdown image (initially hidden, will be updated during countdown)
    this.countdownText = this.add.image(
      this.centerX,
      this.centerY,
      'countdown-3'
    ).setScale(0).setOrigin(0.5);
    this.countdownPanel.add(this.countdownText);
  }

  /**
   * Start countdown sequence
   * Using image-based countdown like in matching mayhem
   */
  async startCountdown() {
    // Show countdown panel
    this.countdownPanel.visible = true;

    // Countdown sequence with images
    const countdownImages = ['countdown-3', 'countdown-2', 'countdown-1', 'countdown-go'];

    for (let i = 0; i < countdownImages.length; i++) {
      await this.playCountdownStep(countdownImages[i], i === countdownImages.length - 1);
    }

    // Hide countdown panel and start game
    this.countdownPanel.visible = false;
    this.showGameUI();
    this.initializeGame();
  }

  /**
   * Play a single step of the countdown animation
   */
  private playCountdownStep(texture: string, isGo: boolean): Promise<void> {
    return new Promise((resolve) => {
      // Update image texture
      this.countdownText.setTexture(texture);
      this.countdownText.setScale(0);

      // Play appropriate sound
      try {
        this.sound.play(isGo ? 'go' : 'countdown');
      } catch (error) {
        console.warn('Sound playback failed:', error);
      }

      // Scale up animation
      this.tweens.add({
        targets: this.countdownText,
        scale: 0.2, // Same scale as matching mayhem
        duration: 300,
        ease: 'Back.easeOut',
        onComplete: () => {
          // Hold for a moment, then scale down
          this.time.delayedCall(700, () => {
            this.tweens.add({
              targets: this.countdownText,
              scale: 0,
              duration: 300,
              ease: 'Back.easeIn',
              onComplete: () => resolve()
            });
          });
        }
      });
    });
  }

  /**
   * Initialize the game after countdown
   */
  initializeGame() {
    // Generate standard bingo card
    this.bingoCard = this.generateStandardBingoCard();

    // Create bingo board UI
    this.createBingoBoard();

    // Start game timer
    this.startGameTimer();

    // Set initial right turn count
    this.rightTurnCount = Phaser.Math.Between(2, 4);

    // Add first right number after delay
    this.time.delayedCall(1500, () => {
      this.addRightItem();
    });
  }

  /**
   * Initialize the pool of available numbers for calling (1-75)
   * Creates a shuffled array of all valid bingo numbers to ensure random but fair calling order
   * @since 1.0.0
   */
  private initializeAvailableNumbers(): void {
    this.availableNumbers = [];
    for (let i = 1; i <= 75; i++) {
      this.availableNumbers.push(i);
    }
    // Shuffle the available numbers for random calling order
    Phaser.Utils.Array.Shuffle(this.availableNumbers);
  }

  /**
   * Generate standard bingo card following official rules
   * B: 1-15, I: 16-30, N: 31-45 (center FREE), G: 46-60, O: 61-75
   *
   * @returns {BingoCellData[][]} A 5x5 grid representing the bingo card
   * @throws {Error} If unable to generate unique numbers for any column
   * @since 1.0.0
   */
  private generateStandardBingoCard(): BingoCellData[][] {
    const card: BingoCellData[][] = [];

    // Initialize 5x5 grid
    for (let row = 0; row < 5; row++) {
      card[row] = [];
      for (let col = 0; col < 5; col++) {
        const column = GameScene.BINGO_COLUMNS[col];

        // Handle center FREE space
        if (row === GameScene.FREE_SPACE_POSITION.row && col === GameScene.FREE_SPACE_POSITION.col) {
          card[row][col] = {
            column,
            number: 0, // FREE space has no number
            row,
            col,
            isFree: true
          };
        } else {
          // Generate unique number for this column
          const availableInColumn = this.getAvailableNumbersForColumn(column, card);
          const randomIndex = Phaser.Math.Between(0, availableInColumn.length - 1);
          const number = availableInColumn[randomIndex];

          card[row][col] = {
            column,
            number,
            row,
            col,
            isFree: false
          };
        }
      }
    }

    return card;
  }

  /**
   * Get available numbers for a specific column that haven't been used yet
   *
   * @param {BingoColumn} column - The bingo column (B, I, N, G, or O)
   * @param {BingoCellData[][]} currentCard - The current state of the bingo card being generated
   * @returns {number[]} Array of available numbers for the specified column
   * @since 1.0.0
   */
  private getAvailableNumbersForColumn(column: BingoColumn, currentCard: BingoCellData[][]): number[] {
    const range = GameScene.COLUMN_RANGES[column];
    const usedNumbers = new Set<number>();

    // Collect already used numbers in this column
    for (let row = 0; row < currentCard.length; row++) {
      const colIndex = GameScene.BINGO_COLUMNS.indexOf(column);
      if (currentCard[row][colIndex] && !currentCard[row][colIndex].isFree) {
        usedNumbers.add(currentCard[row][colIndex].number);
      }
    }

    // Return available numbers in range
    const available: number[] = [];
    for (let num = range.min; num <= range.max; num++) {
      if (!usedNumbers.has(num)) {
        available.push(num);
      }
    }

    return available;
  }

  /**
   * Create the bingo board with random numbers
   */
  createBingoBoard() {
    const cellSize = 80;
    const cellSpacing = 10;
    const totalWidth = (cellSize + cellSpacing) * 5 - cellSpacing;
    const startX = this.centerX - totalWidth / 2; // Center the board horizontally

    // Calculate vertical positions based on camera height
    const boardCenterY = this.centerY + 50; // Slightly below center
    const headerY = boardCenterY - (cellSize + cellSpacing) * 2.5;
    const gridStartY = headerY + (cellSize + cellSpacing);

    // Create the BINGO header row
    for (let col = 0; col < 5; col++) {
      const x = startX + col * (cellSize + cellSpacing) + cellSize / 2;
      const y = headerY;

      // Create a graphics object for the rounded rectangle
      const graphics = this.add.graphics();

      // Use gradient fill for the header
      graphics.fillGradientStyle(
          0x3066FF, // Top-left: blue
          0x4752FF, // Top-right: blue-purple
          0x5E4DFF, // Bottom-right: more purple
          0x215EFF, // Bottom-left: blue
          1
      );
      graphics.lineStyle(3, 0x00E5AE, 1);

      // Draw rounded rectangle centered at (x, y)
      graphics.fillRoundedRect(x - cellSize/2, y - cellSize/2, cellSize, cellSize, 16);
      graphics.strokeRoundedRect(x - cellSize/2, y - cellSize/2, cellSize, cellSize, 16);

      // Add letter
      this.add.text(
          x,
          y,
          GameScene.BINGO_COLUMNS[col],
          {
            fontFamily: '"TT Neoris", Arial, sans-serif',
            fontSize: '48px',
            color: '#FFFFFF',
            align: 'center',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 3,
            shadow: { offsetX: 2, offsetY: 2, color: '#000000', blur: 2, fill: true }
          }
      ).setOrigin(0.5);
    }

    // Create the main number grid using standard bingo card data
    // Create 5x5 grid of bingo cells (excluding the header row)
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const x = startX + col * (cellSize + cellSpacing) + cellSize / 2;
        const y = gridStartY + row * (cellSize + cellSpacing);

        // Get cell data from generated bingo card
        const cellData = this.bingoCard[row][col];

        // Create cell with proper bingo data
        const cell = new BingoCell(
            this,
            x,
            y,
            cellData.column,
            cellData.isFree ? 0 : cellData.number, // Use 0 for FREE space
            cellData.isFree
        );

        // Hide letter for main board (except FREE space)
        if (!cellData.isFree) {
          cell.letterText.alpha = 0;
        }

        // Initially set scale to 0 for animation
        cell.setScale(0);

        // Add to scene and store reference
        this.bgoCells.push(cell);

        // Create staggered animation for each cell based on row and column
        // This creates a wave-like effect as cells appear
        this.time.delayedCall(150 * row + 50 * col, () => {
          this.tweens.add({
            targets: cell,
            scale: 1,
            duration: 300,
            ease: 'Back.Out'
          });
        });
      }
    }
  }

  /**
   * Start the game timer
   * Equivalent to TimerCoroutine in Unity
   */
  startGameTimer() {
    const totalSeconds = 300;
    let timeRemaining = totalSeconds;

    // Update timer display once without creating the timer event
    this.updateGameTimer(timeRemaining, totalSeconds);

    // TESTING: Disable timer countdown
    this.gameTimerEvent = this.time.addEvent({
      delay: 1000,
      callback: () => {
        // Decrease time
        timeRemaining--;

        // Update timer display
        this.updateGameTimer(timeRemaining, totalSeconds);

        // Check if timer has reached zero
        if (timeRemaining <= 0) {
          this.endGame();
        }
      },
      repeat: totalSeconds - 1
    });
  }

  /**
   * Update game timer display
   * @param {number} timeRemaining - Seconds remaining
   * @param {number} _totalSeconds - Total seconds for the timer (unused)
   */
  private updateGameTimer(timeRemaining: number, _totalSeconds: number): void {
    // Update timer text
    this.timerText.setText(timeRemaining.toString());

    // Change color based on time remaining
    if (timeRemaining <= 10) {
      this.timerText.setColor('#FF4D4D'); // Red
    } else if (timeRemaining <= 20) {
      this.timerText.setColor('#FFCC00'); // Yellow
    } else {
      this.timerText.setColor('#389CFF'); // Blue
    }
  }

  /**
   * Calculate positions for right panel items
   */
  private calculateRightPositions(): { x: number; y: number }[] {
    const positions = [];
    const cellSize = 80;
    const cellSpacing = 10;
    const totalWidth = (cellSize + cellSpacing) * 5 - cellSpacing;
    const startX = this.centerX - totalWidth / 2; // Center horizontally

    // Position between header and timer
    const panelY = 170;

    // Create positions for 5 items (0-4 indices)
    for (let i = 0; i < 5; i++) {
      positions.push({
        x: startX + i * (cellSize + cellSpacing) + cellSize / 2,
        y: panelY
      });
    }

    return positions;
  }

  /**
   * Get position for right panel item by index
   * @param {number} index - Position index
   * @returns {object} Position with x and y coordinates
   */
  public getRightPosition(index: number): { x: number; y: number } {
    if (index >= 0 && index < this.rightPositions.length) {
      return this.rightPositions[index];
    }
    return { x: this.centerX + 100, y: this.centerY };
  }

  /**
   * Call the next bingo number following standard bingo rules
   * Numbers are called sequentially from the shuffled pool of 1-75
   *
   * @returns {CalledNumber | null} The called number with metadata, or null if no numbers remain
   * @since 1.0.0
   */
  private callNextBingoNumber(): CalledNumber | null {
    if (this.availableNumbers.length === 0) {
      // All numbers have been called
      return null;
    }

    // Get the next number from the shuffled pool
    const number = this.availableNumbers.shift()!;
    const column = this.getColumnForNumber(number);

    // Create called number record
    const calledNumber: CalledNumber = {
      column,
      number,
      callOrder: ++this.currentCallOrder,
      timestamp: Date.now()
    };

    // Add to called numbers history
    this.calledNumbers.push(calledNumber);

    return calledNumber;
  }

  /**
   * Get the bingo column letter for a given number
   *
   * @param {number} number - The bingo number (1-75)
   * @returns {BingoColumn} The corresponding column letter (B, I, N, G, or O)
   * @throws {Error} If the number is not in the valid range (1-75)
   * @since 1.0.0
   */
  private getColumnForNumber(number: number): BingoColumn {
    if (number >= 1 && number <= 15) return 'B';
    if (number >= 16 && number <= 30) return 'I';
    if (number >= 31 && number <= 45) return 'N';
    if (number >= 46 && number <= 60) return 'G';
    if (number >= 61 && number <= 75) return 'O';
    throw new Error(`Invalid bingo number: ${number}`);
  }

  /**
   * Add a new number to the right panel using standard bingo calling
   * Equivalent to addRightItem in Unity
   */
  private addRightItem(): void {
    if (this.gameEnd) return;

    // Call the next bingo number
    const calledNumber = this.callNextBingoNumber();

    if (!calledNumber) {
      // No more numbers to call - end game
      this.endGame();
      return;
    }

    // Play sound
    this.sound.play('number-appear');

    // Move existing right numbers down
    this.moveExistingRightItems();

    // Get position for new item
    const position = this.getRightPosition(4);

    // Create new right number with the called number
    const rightNumber = new RightNumber(
        this,
        position.x,
        position.y,
        4,
        calledNumber.column,
        calledNumber.number
    );

    // Add to right panel
    this.rightNumbers.push(rightNumber);

    // Set the flag to indicate a right item is scheduled
    this.isRightItemScheduled = true;

    // Schedule next right item
    this.time.delayedCall(2400, () => {
      if (!this.gameEnd) {
        this.isRightItemScheduled = false;
        this.addRightItem();
      }
    });
  }

  /**
   * Move existing right items down
   */
  moveExistingRightItems() {
    for (const item of this.rightNumbers) {
      // Decrease index
      item.moveToPosition(item.rightIndex - 1);
    }

    // Filter out destroyed items
    this.rightNumbers = this.rightNumbers.filter(item => item.rightIndex >= 0);
  }

  /**
   * Check if a cell matches any right panel item
   * @param {BingoCell} cell - The cell to check
   */
  public checkForMatch(cell: BingoCell): void {
    if (this.gameEnd) return;

    // Find matching right number
    const matchingNumber = this.rightNumbers.find(item => item.name === cell.name);

    if (matchingNumber) {
      // Mark the cell
      cell.mark();

      // Remove any highlight effects (if they exist)
      this.children.list.forEach(child => {
        if (child.type === 'Graphics' &&
            (child as any).x === cell.x - 40 &&
            (child as any).y === cell.y - 40) {
          child.destroy();
        }
      });

      // Add score based on timer value
      this.addScore(matchingNumber.getScore());

      // Play sound
      this.sound.play('match');

      // Remove the matching number from the array
      this.rightNumbers = this.rightNumbers.filter(item => item !== matchingNumber);

      // Destroy the matching number
      matchingNumber.destroy();

      // Check for winning patterns after marking the cell
      const winResult = this.checkForWinningPatterns();
      if (winResult.hasWon) {
        this.handleBingoWin(winResult);
        return;
      }

      // Check if we need to add a new item
      this.rightTurnCount--;
      if (this.rightTurnCount <= 0 && !this.gameEnd && !this.isRightItemScheduled) {
        this.time.delayedCall(450, () => {
          if (!this.isRightItemScheduled) {
            this.addRightItem();
          }
        });
      }
    }
  }

  /**
   * Handle a bingo win
   */
  private handleBingoWin(winResult: WinResult): void {
    if (this.gameEnd) return;

    // Set game end flag
    this.gameEnd = true;

    // Stop game timer
    if (this.gameTimerEvent) {
      this.gameTimerEvent.remove();
    }

    // Add bonus score based on pattern type
    const bonusScore = this.getBonusScoreForPattern(winResult.pattern!);
    this.addScore(bonusScore);

    // Create win announcement
    this.createWinAnnouncement(winResult);

    // Create celebration effects
    this.createCelebrationEffects();

    // Transition to GameEndScene after delay
    this.time.delayedCall(2000, () => {
      this.scene.start('GameEndScene', {
        score: this.score,
        winPattern: winResult.pattern
      });
    });
  }

  /**
   * Get bonus score based on winning pattern
   */
  private getBonusScoreForPattern(pattern: WinningPattern): number {
    switch (pattern) {
      case 'horizontal':
      case 'vertical':
      case 'diagonal':
        return 500;
      case 'fourCorners':
        return 750;
      case 'fullCard':
        return 1000;
      default:
        return 0;
    }
  }

  /**
   * Create win announcement display
   */
  private createWinAnnouncement(winResult: WinResult): void {
    const patternNames = {
      horizontal: 'LINE BINGO!',
      vertical: 'LINE BINGO!',
      diagonal: 'DIAGONAL BINGO!',
      fourCorners: 'FOUR CORNERS!',
      fullCard: 'FULL CARD!'
    };

    const announcement = this.add.text(
      this.centerX,
      this.centerY - 100,
      patternNames[winResult.pattern!],
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize: '64px',
        color: '#FFD700',
        align: 'center',
        fontStyle: 'bold',
        stroke: '#000000',
        strokeThickness: 4,
        shadow: { offsetX: 3, offsetY: 3, color: '#000000', blur: 5, fill: true }
      }
    ).setOrigin(0.5).setDepth(10);

    // Animate the announcement
    announcement.setScale(0);
    this.tweens.add({
      targets: announcement,
      scale: 1,
      duration: 500,
      ease: 'Back.Out',
      onComplete: () => {
        // Pulse animation
        this.tweens.add({
          targets: announcement,
          scale: { from: 1, to: 1.1 },
          duration: 800,
          yoyo: true,
          repeat: -1,
          ease: 'Sine.easeInOut'
        });
      }
    });
  }

  /**
   * Check for winning patterns on the bingo card
   * Returns the first winning pattern found, if any
   */
  private checkForWinningPatterns(): WinResult {
    // Check horizontal lines (rows)
    for (let row = 0; row < 5; row++) {
      if (this.isRowComplete(row)) {
        return {
          hasWon: true,
          pattern: 'horizontal',
          winningCells: this.bingoCard[row]
        };
      }
    }

    // Check vertical lines (columns)
    for (let col = 0; col < 5; col++) {
      if (this.isColumnComplete(col)) {
        const winningCells = this.bingoCard.map(row => row[col]);
        return {
          hasWon: true,
          pattern: 'vertical',
          winningCells
        };
      }
    }

    // Check diagonal lines
    if (this.isDiagonalComplete('main')) {
      const winningCells = [];
      for (let i = 0; i < 5; i++) {
        winningCells.push(this.bingoCard[i][i]);
      }
      return {
        hasWon: true,
        pattern: 'diagonal',
        winningCells
      };
    }

    if (this.isDiagonalComplete('anti')) {
      const winningCells = [];
      for (let i = 0; i < 5; i++) {
        winningCells.push(this.bingoCard[i][4 - i]);
      }
      return {
        hasWon: true,
        pattern: 'diagonal',
        winningCells
      };
    }

    // Check four corners
    if (this.isFourCornersComplete()) {
      const winningCells = [
        this.bingoCard[0][0], // Top-left
        this.bingoCard[0][4], // Top-right
        this.bingoCard[4][0], // Bottom-left
        this.bingoCard[4][4]  // Bottom-right
      ];
      return {
        hasWon: true,
        pattern: 'fourCorners',
        winningCells
      };
    }

    // Check full card
    if (this.isFullCardComplete()) {
      const winningCells = this.bingoCard.flat();
      return {
        hasWon: true,
        pattern: 'fullCard',
        winningCells
      };
    }

    return { hasWon: false };
  }

  /**
   * Check if a specific row is complete
   */
  private isRowComplete(row: number): boolean {
    for (let col = 0; col < 5; col++) {
      const cellData = this.bingoCard[row][col];
      const cell = this.findBingoCellByPosition(row, col);
      if (!cell || (!cell.marked && !cellData.isFree)) {
        return false;
      }
    }
    return true;
  }

  /**
   * Check if a specific column is complete
   */
  private isColumnComplete(col: number): boolean {
    for (let row = 0; row < 5; row++) {
      const cellData = this.bingoCard[row][col];
      const cell = this.findBingoCellByPosition(row, col);
      if (!cell || (!cell.marked && !cellData.isFree)) {
        return false;
      }
    }
    return true;
  }

  /**
   * Check if a diagonal is complete
   */
  private isDiagonalComplete(type: 'main' | 'anti'): boolean {
    for (let i = 0; i < 5; i++) {
      const row = i;
      const col = type === 'main' ? i : 4 - i;
      const cellData = this.bingoCard[row][col];
      const cell = this.findBingoCellByPosition(row, col);
      if (!cell || (!cell.marked && !cellData.isFree)) {
        return false;
      }
    }
    return true;
  }

  /**
   * Check if four corners are complete
   */
  private isFourCornersComplete(): boolean {
    const corners = [
      { row: 0, col: 0 }, // Top-left
      { row: 0, col: 4 }, // Top-right
      { row: 4, col: 0 }, // Bottom-left
      { row: 4, col: 4 }  // Bottom-right
    ];

    for (const corner of corners) {
      const cell = this.findBingoCellByPosition(corner.row, corner.col);
      if (!cell || !cell.marked) {
        return false;
      }
    }
    return true;
  }

  /**
   * Check if the full card is complete
   */
  private isFullCardComplete(): boolean {
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const cellData = this.bingoCard[row][col];
        const cell = this.findBingoCellByPosition(row, col);
        if (!cell || (!cell.marked && !cellData.isFree)) {
          return false;
        }
      }
    }
    return true;
  }

  /**
   * Find a bingo cell by its grid position
   */
  private findBingoCellByPosition(row: number, col: number): BingoCell | null {
    const index = row * 5 + col;
    return this.bgoCells[index] || null;
  }

  /**
   * Find a bingo cell by name
   * @param {string} name - The name of the cell (e.g., "B12")
   * @returns {BingoCell|null} The matching cell or null if not found
   */
  public findBingoCellByName(name: string): BingoCell | null {
    return this.bgoCells.find(cell => cell.name === name) || null;
  }

  /**
   * Add score and update display
   * @param {number} value - Score to add
   */
  private addScore(value: number): void {
    this.score += value;

    // Update score display
    this.scoreText.setText(this.score.toString());

    // Create score popup
    this.createScorePopup(value);
  }

  /**
   * Create score popup animation
   * @param {number} value - Score value to show
   */
  private createScorePopup(value: number): void {
    // Create text - position dynamically
    const popup = this.add.text(this.centerX, 80, `+${value}`, {
      fontFamily: '"TT Neoris", Arial, sans-serif',
      fontSize: '32px',
      color: '#4CAF50',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // Animate popup
    this.tweens.add({
      targets: popup,
      y: '-=50',
      alpha: { from: 1, to: 0 },
      scale: { from: 1, to: 1.5 },
      duration: 1000,
      ease: 'Power2',
      onComplete: () => {
        popup.destroy();
      }
    });
  }

  /**
   * End the game (called when timer runs out or no more numbers available)
   * Equivalent to when GameEnd is set to true in Unity
   */
  endGame() {
    if (this.gameEnd) return;

    // Set game end flag
    this.gameEnd = true;

    // Stop game timer
    if (this.gameTimerEvent) {
      this.gameTimerEvent.remove();
    }

    // Create timeout announcement
    this.createTimeoutAnnouncement();

    // Create celebration effects
    this.createCelebrationEffects();

    // Transition to GameEndScene after delay
    this.time.delayedCall(2000, () => {
      this.scene.start('GameEndScene', {
        score: this.score,
        winPattern: 'timeout'
      });
    });
  }

  /**
   * Create timeout announcement when game ends without bingo
   */
  private createTimeoutAnnouncement(): void {
    const announcement = this.add.text(
      this.centerX,
      this.centerY - 100,
      'TIME\'S UP!',
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize: '64px',
        color: '#FF6B6B',
        align: 'center',
        fontStyle: 'bold',
        stroke: '#000000',
        strokeThickness: 4,
        shadow: { offsetX: 3, offsetY: 3, color: '#000000', blur: 5, fill: true }
      }
    ).setOrigin(0.5).setDepth(10);

    // Animate the announcement
    announcement.setScale(0);
    this.tweens.add({
      targets: announcement,
      scale: 1,
      duration: 500,
      ease: 'Back.Out'
    });
  }

  /**
   * Create celebration effects for game end
   * Equivalent to HandleGameEnd in Unity
   */
  createCelebrationEffects() {
    // Play win sound
    this.sound.play('win');

    // Create particles on each cell
    for (const cell of this.bgoCells) {
      // Mark unmarked cells
      if (!cell.marked) {
        cell.mark();
      }

      // Add delayed win particles
      this.time.delayedCall(Phaser.Math.Between(0, 1000), () => {
        cell.createWinParticles();
      });
    }
  }

  /**
   * Hide game UI elements during countdown
   */
  private hideGameUI(): void {
    this.scoreLabel.setVisible(false);
    this.scoreText.setVisible(false);
    this.timerLabel.setVisible(false);
    this.timerText.setVisible(false);
  }

  /**
   * Show game UI elements after countdown
   */
  private showGameUI(): void {
    this.scoreLabel.setVisible(true);
    this.scoreText.setVisible(true);
    this.timerLabel.setVisible(true);
    this.timerText.setVisible(true);
  }
}
